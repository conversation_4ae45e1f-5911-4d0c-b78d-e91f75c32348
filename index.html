
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>级联选择器示例</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        // Mock数据
        const mockData = [
          {
            title: "手机",
            id: 1,
            flag: "brand",
            trPhase: null,
            child: [
              {
                title: "国内",
                id: 1,
                flag: "version",
                trPhase: null,
                child: [
                  {
                    title: "长沙蓝思",
                    id: "180312",
                    flag: "factory",
                    trPhase: null,
                    child: [
                      {
                        title: "O12U",
                        id: "O12U",
                        flag: "project",
                        trPhase: null,
                        child: [],
                      },
                      {
                        title: "O3",
                        id: "O3",
                        flag: "project",
                        trPhase: "TR6",
                        child: [],
                      },
                      {
                        title: "P1",
                        id: "P1",
                        flag: "project",
                        trPhase: "TR6",
                        child: [],
                      }
                    ],
                  },
                  {
                    title: "南昌勤胜",
                    id: "129797",
                    flag: "factory",
                    trPhase: null,
                    child: [
                      {
                        title: "L16S",
                        id: "L16S",
                        flag: "project",
                        trPhase: null,
                        child: [],
                      },
                      {
                        title: "M2",
                        id: "M2",
                        flag: "project",
                        trPhase: null,
                        child: [],
                      }
                    ],
                  }
                ],
              },
              {
                title: "印度",
                id: 3,
                flag: "version",
                trPhase: null,
                child: [
                  {
                    title: "印度光弘",
                    id: "123503",
                    flag: "factory",
                    trPhase: null,
                    child: [
                      {
                        title: "M20",
                        id: "M20",
                        flag: "project",
                        trPhase: null,
                        child: [],
                      },
                      {
                        title: "N16",
                        id: "N16",
                        flag: "project",
                        trPhase: "TR6",
                        child: [],
                      }
                    ],
                  }
                ],
              }
            ],
          },
          {
            title: "平板",
            id: 2,
            flag: "brand",
            trPhase: null,
            child: [
              {
                title: "国内",
                id: 1,
                flag: "version",
                trPhase: null,
                child: [
                  {
                    title: "集贤比亚迪",
                    id: "160434",
                    flag: "factory",
                    trPhase: null,
                    child: [
                      {
                        title: "L81A",
                        id: "L81A",
                        flag: "project",
                        trPhase: null,
                        child: [],
                      },
                      {
                        title: "M81",
                        id: "M81",
                        flag: "project",
                        trPhase: null,
                        child: [],
                      }
                    ],
                  }
                ],
              }
            ],
          }
        ];

        const CascadeSelect = () => {
          const [selectedBrand, setSelectedBrand] = React.useState('');
          const [selectedVersion, setSelectedVersion] = React.useState('');
          const [selectedFactory, setSelectedFactory] = React.useState('');
          const [selectedProject, setSelectedProject] = React.useState('');

          const [versions, setVersions] = React.useState([]);
          const [factories, setFactories] = React.useState([]);
          const [projects, setProjects] = React.useState([]);

          // 获取品牌列表
          const brands = mockData.filter(item => item.flag === 'brand');

          // 当品牌改变时，更新版本列表并重置后续选择
          React.useEffect(() => {
            if (selectedBrand) {
              const brand = brands.find(b => b.id.toString() === selectedBrand);
              const newVersions = brand ? brand.child : [];
              setVersions(newVersions);
              
              // 自动选择第一个版本
              if (newVersions.length > 0) {
                setSelectedVersion(newVersions[0].id.toString());
              } else {
                setSelectedVersion('');
              }
              
              setSelectedFactory('');
              setSelectedProject('');
              setFactories([]);
              setProjects([]);
            } else {
              setVersions([]);
              setFactories([]);
              setProjects([]);
            }
          }, [selectedBrand]);

          // 当版本改变时，更新工厂列表并重置后续选择
          React.useEffect(() => {
            if (selectedVersion && versions.length > 0) {
              const version = versions.find(v => v.id.toString() === selectedVersion);
              const newFactories = version ? version.child : [];
              setFactories(newFactories);
              
              // 自动选择第一个工厂
              if (newFactories.length > 0) {
                setSelectedFactory(newFactories[0].id);
              } else {
                setSelectedFactory('');
              }
              
              setSelectedProject('');
              setProjects([]);
            } else {
              setFactories([]);
              setProjects([]);
            }
          }, [selectedVersion, versions]);

          // 当工厂改变时，更新项目列表并重置项目选择
          React.useEffect(() => {
            if (selectedFactory && factories.length > 0) {
              const factory = factories.find(f => f.id === selectedFactory);
              const newProjects = factory ? factory.child : [];
              setProjects(newProjects);
              
              // 自动选择第一个项目
              if (newProjects.length > 0) {
                setSelectedProject(newProjects[0].id);
              } else {
                setSelectedProject('');
              }
            } else {
              setProjects([]);
            }
          }, [selectedFactory, factories]);

          const selectStyle = {
            padding: '10px 15px',
            margin: '0 15px 15px 0',
            border: '2px solid #e1e5e9',
            borderRadius: '8px',
            fontSize: '16px',
            minWidth: '180px',
            backgroundColor: '#fff',
            cursor: 'pointer',
            transition: 'all 0.3s ease'
          };

          const disabledStyle = {
            ...selectStyle,
            backgroundColor: '#f8f9fa',
            color: '#6c757d',
            cursor: 'not-allowed'
          };

          const containerStyle = {
            maxWidth: '800px',
            margin: '0 auto',
            padding: '30px',
            fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
            backgroundColor: '#f8f9fa',
            minHeight: '100vh'
          };

          const cardStyle = {
            backgroundColor: '#fff',
            padding: '30px',
            borderRadius: '12px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
          };

          const titleStyle = {
            color: '#2c3e50',
            marginBottom: '25px',
            fontSize: '24px',
            fontWeight: '600'
          };

          const resultStyle = {
            padding: '20px',
            backgroundColor: '#e8f5e8',
            borderRadius: '8px',
            marginTop: '25px',
            border: '1px solid #28a745'
          };

          return (
            <div style={containerStyle}>
              <div style={cardStyle}>
                <h2 style={titleStyle}>产品级联选择器</h2>
                
                <div style={{ marginBottom: '25px' }}>
                  <div style={{ marginBottom: '15px' }}>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500', color: '#495057' }}>
                      选择品牌：
                    </label>
                    <select
                      style={selectStyle}
                      value={selectedBrand}
                      onChange={(e) => setSelectedBrand(e.target.value)}
                    >
                      <option value="">请选择品牌</option>
                      {brands.map(brand => (
                        <option key={brand.id} value={brand.id}>
                          {brand.title}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div style={{ marginBottom: '15px' }}>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500', color: '#495057' }}>
                      选择版本：
                    </label>
                    <select
                      style={!selectedBrand ? disabledStyle : selectStyle}
                      value={selectedVersion}
                      onChange={(e) => setSelectedVersion(e.target.value)}
                      disabled={!selectedBrand}
                    >
                      <option value="">请选择版本</option>
                      {versions.map(version => (
                        <option key={version.id} value={version.id}>
                          {version.title}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div style={{ marginBottom: '15px' }}>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500', color: '#495057' }}>
                      选择工厂：
                    </label>
                    <select
                      style={!selectedVersion ? disabledStyle : selectStyle}
                      value={selectedFactory}
                      onChange={(e) => setSelectedFactory(e.target.value)}
                      disabled={!selectedVersion}
                    >
                      <option value="">请选择工厂</option>
                      {factories.map(factory => (
                        <option key={factory.id} value={factory.id}>
                          {factory.title}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div style={{ marginBottom: '15px' }}>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500', color: '#495057' }}>
                      选择项目：
                    </label>
                    <select
                      style={!selectedFactory ? disabledStyle : selectStyle}
                      value={selectedProject}
                      onChange={(e) => setSelectedProject(e.target.value)}
                      disabled={!selectedFactory}
                    >
                      <option value="">请选择项目</option>
                      {projects.map(project => (
                        <option key={project.id} value={project.id}>
                          {project.title} {project.trPhase && `(${project.trPhase})`}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {selectedProject && (
                  <div style={resultStyle}>
                    <h4 style={{ color: '#155724', marginBottom: '15px', fontSize: '18px' }}>选择结果：</h4>
                    <div style={{ lineHeight: '1.6' }}>
                      <p><strong>品牌:</strong> {brands.find(b => b.id.toString() === selectedBrand)?.title}</p>
                      <p><strong>版本:</strong> {versions.find(v => v.id.toString() === selectedVersion)?.title}</p>
                      <p><strong>工厂:</strong> {factories.find(f => f.id === selectedFactory)?.title}</p>
                      <p><strong>项目:</strong> {projects.find(p => p.id === selectedProject)?.title}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          );
        };

        // 渲染应用
        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<CascadeSelect />);
    </script>
</body>
</html>

