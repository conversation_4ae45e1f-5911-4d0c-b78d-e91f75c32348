
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>级联选择器示例</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        // Mock数据（简化版，用于演示）
        const mockData = [
          {
            title: "手机",
            id: 1,
            flag: "brand",
            trPhase: null,
            child: [
              {
                title: "国内",
                id: 1,
                flag: "version",
                trPhase: null,
                child: [
                  {
                    title: "长沙蓝思",
                    id: "180312",
                    flag: "factory",
                    trPhase: null,
                    child: [
                      {
                        title: "O12U",
                        id: "O12U",
                        flag: "project",
                        trPhase: null,
                        child: [],
                      },
                      {
                        title: "O3",
                        id: "O3",
                        flag: "project",
                        trPhase: "TR6",
                        child: [],
                      },
                      {
                        title: "P1",
                        id: "P1",
                        flag: "project",
                        trPhase: "TR6",
                        child: [],
                      }
                    ],
                  },
                  {
                    title: "南昌勤胜",
                    id: "129797",
                    flag: "factory",
                    trPhase: null,
                    child: [
                      {
                        title: "L16S",
                        id: "L16S",
                        flag: "project",
                        trPhase: null,
                        child: [],
                      },
                      {
                        title: "M2",
                        id: "M2",
                        flag: "project",
                        trPhase: null,
                        child: [],
                      }
                    ],
                  }
                ],
              },
              {
                title: "印度",
                id: 3,
                flag: "version",
                trPhase: null,
                child: [
                  {
                    title: "印度光弘",
                    id: "123503",
                    flag: "factory",
                    trPhase: null,
                    child: [
                      {
                        title: "M20",
                        id: "M20",
                        flag: "project",
                        trPhase: null,
                        child: [],
                      },
                      {
                        title: "N16",
                        id: "N16",
                        flag: "project",
                        trPhase: "TR6",
                        child: [],
                      }
                    ],
                  }
                ],
              }
            ],
          },
          {
            title: "平板",
            id: 2,
            flag: "brand",
            trPhase: null,
            child: [
              {
                title: "国内",
                id: 1,
                flag: "version",
                trPhase: null,
                child: [
                  {
                    title: "集贤比亚迪",
                    id: "160434",
                    flag: "factory",
                    trPhase: null,
                    child: [
                      {
                        title: "L81A",
                        id: "L81A",
                        flag: "project",
                        trPhase: null,
                        child: [],
                      },
                      {
                        title: "M81",
                        id: "M81",
                        flag: "project",
                        trPhase: null,
                        child: [],
                      }
                    ],
                  }
                ],
              }
            ],
          }
        ];

        // useCascadeSelect Hook 实现
        const useCascadeSelect = (data, initialSelected = null) => {
          const [selectedData, setSelectedDataState] = React.useState({});

          // 根据选中数据构建层级路径
          const buildPath = React.useCallback((selected) => {
            if (!data || data.length === 0) return [];

            const path = [];
            let currentLevel = data;

            // 获取所有可能的flag类型，按层级顺序
            const getAllFlags = (items, flags = []) => {
              if (!items || items.length === 0) return flags;

              const currentFlag = items[0]?.flag;
              if (currentFlag && !flags.includes(currentFlag)) {
                flags.push(currentFlag);
              }

              // 递归检查子级
              for (const item of items) {
                if (item.child && item.child.length > 0) {
                  getAllFlags(item.child, flags);
                  break;
                }
              }

              return flags;
            };

            const flagOrder = getAllFlags(data);

            // 按flag顺序构建路径
            for (const flag of flagOrder) {
              if (!currentLevel || currentLevel.length === 0) break;

              let selectedItem;

              if (selected && selected[flag]) {
                selectedItem = currentLevel.find(item =>
                  item.flag === flag &&
                  (item.id === selected[flag] || item.id.toString() === selected[flag].toString())
                );
              }

              if (!selectedItem) {
                selectedItem = currentLevel.find(item => item.flag === flag);
              }

              if (selectedItem) {
                path.push(selectedItem);
                currentLevel = selectedItem.child || [];
              } else {
                break;
              }
            }

            return path;
          }, [data]);

          // 根据路径构建选中数据对象
          const buildSelectedData = React.useCallback((path) => {
            const result = {};
            path.forEach(item => {
              if (item.flag) {
                result[item.flag] = item.id;
              }
            });
            return result;
          }, []);

          // 初始化选中数据
          React.useEffect(() => {
            if (data && data.length > 0) {
              const initialPath = buildPath(initialSelected);
              const initialSelectedData = buildSelectedData(initialPath);
              setSelectedDataState(initialSelectedData);
            }
          }, [data, initialSelected, buildPath, buildSelectedData]);

          // 设置选中数据的方法
          const setSelectedData = React.useCallback((newSelected) => {
            if (typeof newSelected === 'function') {
              setSelectedDataState(prev => {
                const updated = newSelected(prev);
                const newPath = buildPath(updated);
                return buildSelectedData(newPath);
              });
            } else {
              const newPath = buildPath(newSelected);
              setSelectedDataState(buildSelectedData(newPath));
            }
          }, [buildPath, buildSelectedData]);

          // 根据当前选中数据构建完整路径
          const currentPath = buildPath(selectedData);

          // 构建平铺的层级关系数组
          const flattenedSelections = currentPath.map(item => ({
            id: item.id,
            title: item.title,
            flag: item.flag,
            trPhase: item.trPhase
          }));

          // 获取指定层级的选项列表
          const getOptionsForLevel = React.useCallback((flag) => {
            if (!data || data.length === 0) return [];

            let currentLevel = data;
            const path = buildPath(selectedData);

            for (const item of path) {
              if (item.flag === flag) {
                return currentLevel.filter(option => option.flag === flag);
              }

              if (item.child && item.child.length > 0) {
                currentLevel = item.child;
              }
            }

            return currentLevel.filter(option => option.flag === flag);
          }, [data, selectedData, buildPath]);

          // 处理单个层级的选择变更
          const handleLevelChange = React.useCallback((flag, value) => {
            setSelectedData(prev => {
              const newSelected = { ...prev };
              newSelected[flag] = value;

              // 获取所有flag的顺序
              const getAllFlags = (items, flags = []) => {
                if (!items || items.length === 0) return flags;

                const currentFlag = items[0]?.flag;
                if (currentFlag && !flags.includes(currentFlag)) {
                  flags.push(currentFlag);
                }

                for (const item of items) {
                  if (item.child && item.child.length > 0) {
                    getAllFlags(item.child, flags);
                    break;
                  }
                }

                return flags;
              };

              const flagOrder = getAllFlags(data);
              const currentIndex = flagOrder.indexOf(flag);

              // 清除当前层级之后的所有选择
              if (currentIndex !== -1) {
                for (let i = currentIndex + 1; i < flagOrder.length; i++) {
                  delete newSelected[flagOrder[i]];
                }
              }

              return newSelected;
            });
          }, [setSelectedData, data]);

          return [selectedData, flattenedSelections, setSelectedData, { getOptionsForLevel, handleLevelChange }];
        };

        // 主组件
        const CascadeSelectDemo = () => {
          // 示例1: 默认选择
          const [selectedData1, flattenedSelections1, setSelectedData1, helpers1] = useCascadeSelect(mockData);

          // 示例2: 指定初始选中项
          const initialSelected = {
            brand: 1,
            version: 1,
            factory: "180312",
            project: "O3"
          };
          const [selectedData2, flattenedSelections2, setSelectedData2, helpers2] = useCascadeSelect(mockData, initialSelected);

          // 层级配置
          const levels = [
            { flag: 'brand', label: '品牌' },
            { flag: 'version', label: '版本' },
            { flag: 'factory', label: '工厂' },
            { flag: 'project', label: '项目' }
          ];

          // 检查某个层级是否应该显示
          const shouldShowLevel = (currentFlag, selectedData) => {
            const currentIndex = levels.findIndex(level => level.flag === currentFlag);
            if (currentIndex === 0) return true;

            const previousFlag = levels[currentIndex - 1]?.flag;
            return previousFlag && selectedData[previousFlag];
          };

          // 渲染选择器
          const renderSelector = (flag, label, selectedData, helpers, disabled = false) => {
            if (!shouldShowLevel(flag, selectedData)) return null;

            const options = helpers.getOptionsForLevel(flag);

            return (
              <div style={{ marginBottom: '15px' }}>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontWeight: '500',
                  color: disabled ? '#6c757d' : '#495057'
                }}>
                  {label}：
                </label>
                <select
                  style={{
                    padding: '10px 15px',
                    border: '2px solid #e1e5e9',
                    borderRadius: '8px',
                    fontSize: '16px',
                    minWidth: '200px',
                    backgroundColor: disabled ? '#f8f9fa' : '#fff',
                    color: disabled ? '#6c757d' : '#000',
                    cursor: disabled ? 'not-allowed' : 'pointer',
                    transition: 'all 0.3s ease'
                  }}
                  value={selectedData[flag] || ''}
                  onChange={(e) => helpers.handleLevelChange(flag, e.target.value)}
                  disabled={disabled}
                >
                  <option value="">请选择{label}</option>
                  {options.map(item => (
                    <option key={item.id} value={item.id}>
                      {item.title} {item.trPhase && `(${item.trPhase})`}
                    </option>
                  ))}
                </select>
              </div>
            );
          };

          // 样式定义
          const containerStyle = {
            maxWidth: '1200px',
            margin: '0 auto',
            padding: '20px',
            fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
            backgroundColor: '#f8f9fa',
            minHeight: '100vh'
          };

          const cardStyle = {
            backgroundColor: '#fff',
            padding: '25px',
            borderRadius: '12px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            marginBottom: '20px'
          };

          const titleStyle = {
            color: '#2c3e50',
            marginBottom: '20px',
            fontSize: '20px',
            fontWeight: '600',
            borderBottom: '2px solid #e9ecef',
            paddingBottom: '10px'
          };

          const resultStyle = {
            padding: '15px',
            backgroundColor: '#e8f5e8',
            borderRadius: '8px',
            marginTop: '20px',
            border: '1px solid #28a745'
          };

          const pathStyle = {
            padding: '10px',
            backgroundColor: '#f8f9fa',
            borderRadius: '6px',
            marginTop: '10px',
            fontSize: '14px',
            color: '#495057'
          };

          const buttonStyle = {
            padding: '8px 16px',
            margin: '5px',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500',
            transition: 'all 0.3s ease'
          };

          return (
            <div style={containerStyle}>
              <h1 style={{ textAlign: 'center', color: '#2c3e50', marginBottom: '30px' }}>
                级联选择组件演示
              </h1>

              {/* 示例1: 默认选择 */}
              <div style={cardStyle}>
                <h2 style={titleStyle}>示例1: 默认选择（自动选中第一项）</h2>

                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '15px', marginBottom: '20px' }}>
                  {levels.map(({ flag, label }) => renderSelector(flag, label, selectedData1, helpers1))}
                </div>

                {flattenedSelections1.length > 0 && (
                  <div style={resultStyle}>
                    <h4 style={{ color: '#155724', marginBottom: '10px' }}>选择结果：</h4>
                    <div style={pathStyle}>
                      <strong>选择路径：</strong>
                      {flattenedSelections1.map((item, index) => (
                        <span key={item.flag}>
                          {item.title}
                          {index < flattenedSelections1.length - 1 && ' → '}
                        </span>
                      ))}
                    </div>
                    <div style={pathStyle}>
                      <strong>选中数据：</strong>
                      <pre style={{ margin: '5px 0', fontSize: '12px' }}>
                        {JSON.stringify(selectedData1, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}

                <div style={{ marginTop: '15px' }}>
                  <button
                    style={{ ...buttonStyle, backgroundColor: '#dc3545', color: 'white' }}
                    onClick={() => setSelectedData1({})}
                  >
                    重置选择
                  </button>
                  <button
                    style={{ ...buttonStyle, backgroundColor: '#28a745', color: 'white' }}
                    onClick={() => setSelectedData1({ brand: 2, version: 1 })}
                  >
                    切换到平板
                  </button>
                </div>
              </div>

              {/* 示例2: 指定初始选中项 */}
              <div style={cardStyle}>
                <h2 style={titleStyle}>示例2: 指定初始选中项</h2>
                <p style={{ color: '#6c757d', marginBottom: '15px' }}>
                  初始选中：手机 → 国内 → 长沙蓝思 → O3
                </p>

                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '15px', marginBottom: '20px' }}>
                  {levels.map(({ flag, label }) => renderSelector(flag, label, selectedData2, helpers2))}
                </div>

                {flattenedSelections2.length > 0 && (
                  <div style={resultStyle}>
                    <h4 style={{ color: '#155724', marginBottom: '10px' }}>选择结果：</h4>
                    <div style={pathStyle}>
                      <strong>选择路径：</strong>
                      {flattenedSelections2.map((item, index) => (
                        <span key={item.flag}>
                          {item.title}
                          {item.trPhase && ` (${item.trPhase})`}
                          {index < flattenedSelections2.length - 1 && ' → '}
                        </span>
                      ))}
                    </div>
                    <div style={pathStyle}>
                      <strong>平铺层级数组：</strong>
                      <pre style={{ margin: '5px 0', fontSize: '12px' }}>
                        {JSON.stringify(flattenedSelections2, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}

                <div style={{ marginTop: '15px' }}>
                  <button
                    style={{ ...buttonStyle, backgroundColor: '#007bff', color: 'white' }}
                    onClick={() => setSelectedData2({ brand: 1, version: 3, factory: "123503" })}
                  >
                    切换到印度工厂
                  </button>
                  <button
                    style={{ ...buttonStyle, backgroundColor: '#6f42c1', color: 'white' }}
                    onClick={() => setSelectedData2(initialSelected)}
                  >
                    恢复初始选择
                  </button>
                </div>
              </div>

              {/* API 说明 */}
              <div style={cardStyle}>
                <h2 style={titleStyle}>API 说明</h2>
                <div style={{ fontSize: '14px', lineHeight: '1.6' }}>
                  <p><strong>useCascadeSelect(data, initialSelected?)</strong></p>
                  <ul style={{ paddingLeft: '20px' }}>
                    <li><strong>data</strong>: 全量数据数组</li>
                    <li><strong>initialSelected</strong>: 初始选中项（可选）</li>
                  </ul>
                  <p><strong>返回值：</strong></p>
                  <ul style={{ paddingLeft: '20px' }}>
                    <li><strong>selectedData</strong>: 选中数据对象</li>
                    <li><strong>flattenedSelections</strong>: 平铺层级数组</li>
                    <li><strong>setSelectedData</strong>: 设置选中数据的方法</li>
                    <li><strong>helpers</strong>: 辅助函数对象</li>
                  </ul>
                </div>
              </div>
            </div>
          );
        };

        // 渲染应用
        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<CascadeSelectDemo />);
    </script>
</body>
</html>

