import { useState, useEffect } from 'react';

const useCascadeSelect = (mockData) => {
  const [selectedBrand, setSelectedBrand] = useState('');
  const [selectedVersion, setSelectedVersion] = useState('');
  const [selectedFactory, setSelectedFactory] = useState('');
  const [selectedProject, setSelectedProject] = useState('');

  const [versions, setVersions] = useState([]);
  const [factories, setFactories] = useState([]);
  const [projects, setProjects] = useState([]);

  // 获取品牌列表
  const brands = mockData.filter(item => item.flag === 'brand');

  // 当品牌改变时，更新版本列表并重置后续选择
  useEffect(() => {
    if (selectedBrand) {
      const brand = brands.find(b => b.id.toString() === selectedBrand);
      const newVersions = brand ? brand.child : [];
      setVersions(newVersions);
      
      // 自动选择第一个版本
      if (newVersions.length > 0) {
        setSelectedVersion(newVersions[0].id.toString());
      } else {
        setSelectedVersion('');
      }
      
      setSelectedFactory('');
      setSelectedProject('');
      setFactories([]);
      setProjects([]);
    } else {
      setVersions([]);
      setFactories([]);
      setProjects([]);
    }
  }, [selectedBrand, brands]);

  // 当版本改变时，更新工厂列表并重置后续选择
  useEffect(() => {
    if (selectedVersion && versions.length > 0) {
      const version = versions.find(v => v.id.toString() === selectedVersion);
      const newFactories = version ? version.child : [];
      setFactories(newFactories);
      
      // 自动选择第一个工厂
      if (newFactories.length > 0) {
        setSelectedFactory(newFactories[0].id);
      } else {
        setSelectedFactory('');
      }
      
      setSelectedProject('');
      setProjects([]);
    } else {
      setFactories([]);
      setProjects([]);
    }
  }, [selectedVersion, versions]);

  // 当工厂改变时，更新项目列表并重置项目选择
  useEffect(() => {
    if (selectedFactory && factories.length > 0) {
      const factory = factories.find(f => f.id === selectedFactory);
      const newProjects = factory ? factory.child : [];
      setProjects(newProjects);
      
      // 自动选择第一个项目
      if (newProjects.length > 0) {
        setSelectedProject(newProjects[0].id);
      } else {
        setSelectedProject('');
      }
    } else {
      setProjects([]);
    }
  }, [selectedFactory, factories]);

  return {
    // 选中的值
    selectedBrand,
    selectedVersion,
    selectedFactory,
    selectedProject,
    
    // 设置函数
    setSelectedBrand,
    setSelectedVersion,
    setSelectedFactory,
    setSelectedProject,
    
    // 选项列表
    brands,
    versions,
    factories,
    projects
  };
};

export default useCascadeSelect;