import { useState, useEffect, useCallback } from 'react';

/**
 * 级联选择Hook
 * @param {Array} data - 全量数据数组
 * @param {Object} initialSelected - 初始选中的数据项（可选）
 * @returns {Array} [selectedData, flattenedSelections, levelOptions, setSelectedData, helpers]
 */
const useCascadeSelect = (data, initialSelected = null) => {
  // 存储选中的数据，格式为 { flag1: value1, flag2: value2, ... }
  const [selectedData, setSelectedDataState] = useState({});

  // 根据选中数据构建层级路径
  const buildPath = useCallback((selected) => {
    if (!data || data.length === 0) return [];

    const path = [];
    let currentLevel = data;

    // 获取所有可能的flag类型，按层级顺序
    const getAllFlags = (items, flags = []) => {
      if (!items || items.length === 0) return flags;

      const currentFlag = items[0]?.flag;
      if (currentFlag && !flags.includes(currentFlag)) {
        flags.push(currentFlag);
      }

      // 递归检查子级
      for (const item of items) {
        if (item.child && item.child.length > 0) {
          getAllFlags(item.child, flags);
          break; // 只需要检查第一个有子级的项目即可
        }
      }

      return flags;
    };

    const flagOrder = getAllFlags(data);

    // 按flag顺序构建路径
    for (const flag of flagOrder) {
      if (!currentLevel || currentLevel.length === 0) break;

      let selectedItem;

      if (selected && selected[flag]) {
        // 如果有指定选中项，查找对应项
        selectedItem = currentLevel.find(item =>
          item.flag === flag &&
          (item.id === selected[flag] || item.id.toString() === selected[flag].toString())
        );
      }

      // 如果没有找到指定项或没有指定，选择第一项
      if (!selectedItem) {
        selectedItem = currentLevel.find(item => item.flag === flag);
      }

      if (selectedItem) {
        path.push(selectedItem);
        currentLevel = selectedItem.child || [];
      } else {
        break;
      }
    }

    return path;
  }, [data]);

  // 根据路径构建选中数据对象
  const buildSelectedData = useCallback((path) => {
    const result = {};
    path.forEach(item => {
      if (item.flag) {
        result[item.flag] = item.id;
      }
    });
    return result;
  }, []);

  // 初始化选中数据
  useEffect(() => {
    if (data && data.length > 0) {
      const initialPath = buildPath(initialSelected);
      const initialSelectedData = buildSelectedData(initialPath);
      setSelectedDataState(initialSelectedData);
    }
  }, [data, initialSelected, buildPath, buildSelectedData]);

  // 设置选中数据的方法，支持级联重置
  const setSelectedData = useCallback((newSelected) => {
    if (typeof newSelected === 'function') {
      setSelectedDataState(prev => {
        const updated = newSelected(prev);
        // 重新构建路径以确保级联逻辑正确
        const newPath = buildPath(updated);
        return buildSelectedData(newPath);
      });
    } else {
      // 重新构建路径以确保级联逻辑正确
      const newPath = buildPath(newSelected);
      setSelectedDataState(buildSelectedData(newPath));
    }
  }, [buildPath, buildSelectedData]);

  // 根据当前选中数据构建完整路径
  const currentPath = buildPath(selectedData);

  // 构建平铺的层级关系数组
  const flattenedSelections = currentPath.map(item => ({
    id: item.id,
    title: item.title,
    flag: item.flag,
    trPhase: item.trPhase
  }));

  // 辅助函数：获取指定层级的选项列表
  const getOptionsForLevel = useCallback((flag) => {
    if (!data || data.length === 0) return [];

    let currentLevel = data;
    const path = buildPath(selectedData);

    // 找到目标flag之前的路径
    for (const item of path) {
      if (item.flag === flag) {
        return currentLevel.filter(option => option.flag === flag);
      }

      if (item.child && item.child.length > 0) {
        currentLevel = item.child;
      }
    }

    // 如果路径中没有找到，检查当前层级
    return currentLevel.filter(option => option.flag === flag);
  }, [data, selectedData, buildPath]);

  // 辅助函数：处理单个层级的选择变更
  const handleLevelChange = useCallback((flag, value) => {
    setSelectedData(prev => {
      const newSelected = { ...prev };
      newSelected[flag] = value;

      // 获取所有flag的顺序
      const getAllFlags = (items, flags = []) => {
        if (!items || items.length === 0) return flags;

        const currentFlag = items[0]?.flag;
        if (currentFlag && !flags.includes(currentFlag)) {
          flags.push(currentFlag);
        }

        for (const item of items) {
          if (item.child && item.child.length > 0) {
            getAllFlags(item.child, flags);
            break;
          }
        }

        return flags;
      };

      const flagOrder = getAllFlags(data);
      const currentIndex = flagOrder.indexOf(flag);

      // 清除当前层级之后的所有选择（级联重置）
      if (currentIndex !== -1) {
        for (let i = currentIndex + 1; i < flagOrder.length; i++) {
          delete newSelected[flagOrder[i]];
        }
      }

      return newSelected;
    });
  }, [setSelectedData, data]);

  // 构建每一层的选项列表数据（数组平铺格式）
  const levelOptions = useCallback(() => {
    if (!data || data.length === 0) return [];

    const result = [];
    let currentLevel = data;

    // 获取所有可能的flag类型，按层级顺序
    const getAllFlags = (items, flags = []) => {
      if (!items || items.length === 0) return flags;

      const currentFlag = items[0]?.flag;
      if (currentFlag && !flags.includes(currentFlag)) {
        flags.push(currentFlag);
      }

      for (const item of items) {
        if (item.child && item.child.length > 0) {
          getAllFlags(item.child, flags);
          break;
        }
      }

      return flags;
    };

    const flagOrder = getAllFlags(data);
    let levelIndex = 0;

    // 按层级顺序构建每一层的选项列表
    for (const flag of flagOrder) {
      if (!currentLevel || currentLevel.length === 0) break;

      // 获取当前层级的所有选项
      const levelItems = currentLevel.filter(item => item.flag === flag);

      if (levelItems.length > 0) {
        // 找到当前层级的选中项
        const selectedItem = levelItems.find(item =>
          selectedData[flag] &&
          (item.id === selectedData[flag] || item.id.toString() === selectedData[flag].toString())
        );

        // 构建当前层级的选项数据
        const levelData = {
          level: levelIndex,
          flag: flag,
          options: levelItems.map(item => ({
            id: item.id,
            title: item.title,
            flag: item.flag,
            trPhase: item.trPhase,
            hasChildren: item.child && item.child.length > 0
          })),
          selectedValue: selectedData[flag] || null,
          selectedItem: selectedItem || null
        };

        result.push(levelData);

        // 如果有选中项且有子级，移动到下一层级
        if (selectedItem && selectedItem.child && selectedItem.child.length > 0) {
          currentLevel = selectedItem.child;
        } else {
          // 没有选中项或选中项没有子级，停止构建
          break;
        }
      }

      levelIndex++;
    }

    return result;
  }, [data, selectedData]);

  // 获取当前的层级选项数据
  const currentLevelOptions = levelOptions();

  return [selectedData, flattenedSelections, currentLevelOptions, setSelectedData, { getOptionsForLevel, handleLevelChange }];
};

export default useCascadeSelect;