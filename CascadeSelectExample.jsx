import React from 'react';
import useCascadeSelect from './useCascadeSelect';
import mockData from './mock';

const CascadeSelectExample = () => {
  // 示例1: 默认选择（选中第一项）
  const [selectedData1, flattenedSelections1, setSelectedData1, helpers1] = useCascadeSelect(mockData);

  // 示例2: 指定初始选中项
  const initialSelected = {
    brand: 1,      // 手机
    version: 1,    // 国内
    factory: "180312", // 长沙蓝思
    project: "O3"  // O3项目
  };
  const [selectedData2, flattenedSelections2, setSelectedData2, helpers2] = useCascadeSelect(mockData, initialSelected);
  
  // 渲染选择器的辅助函数
  const renderSelector = (flag, label, selectedData, helpers) => {
    const options = helpers.getOptionsForLevel(flag);
    const shouldShow = flag === 'brand' || selectedData[getPreviousFlag(flag)];

    if (!shouldShow) return null;

    return (
      <div style={{ marginBottom: '10px' }}>
        <label>{label}: </label>
        <select
          value={selectedData[flag] || ''}
          onChange={(e) => helpers.handleLevelChange(flag, e.target.value)}
        >
          <option value="">请选择{label}</option>
          {options.map(item => (
            <option key={item.id} value={item.id}>{item.title}</option>
          ))}
        </select>
      </div>
    );
  };

  // 获取前一个flag的辅助函数
  const getPreviousFlag = (currentFlag) => {
    const flagOrder = ['brand', 'version', 'factory', 'project'];
    const currentIndex = flagOrder.indexOf(currentFlag);
    return currentIndex > 0 ? flagOrder[currentIndex - 1] : null;
  };
  
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>级联选择组件示例</h1>
      
      {/* 示例1: 默认选择 */}
      <div style={{ marginBottom: '40px', border: '1px solid #ccc', padding: '20px', borderRadius: '8px' }}>
        <h2>示例1: 默认选择（选中第一项）</h2>
        
        <div style={{ marginBottom: '20px' }}>
          <h3>选择器:</h3>
          {renderSelector('brand', '品牌', selectedData1, helpers1)}
          {renderSelector('version', '版本', selectedData1, helpers1)}
          {renderSelector('factory', '工厂', selectedData1, helpers1)}
          {renderSelector('project', '项目', selectedData1, helpers1)}
        </div>
        
        <div>
          <h3>输出结果:</h3>
          <p><strong>选中数据对象:</strong></p>
          <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
            {JSON.stringify(selectedData1, null, 2)}
          </pre>
          
          <p><strong>平铺层级数组:</strong></p>
          <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
            {JSON.stringify(flattenedSelections1, null, 2)}
          </pre>
        </div>
      </div>
      
      {/* 示例2: 指定初始选中项 */}
      <div style={{ border: '1px solid #ccc', padding: '20px', borderRadius: '8px' }}>
        <h2>示例2: 指定初始选中项</h2>
        <p>初始选中: 手机 → 国内 → 长沙蓝思 → O3</p>
        
        <div>
          <h3>输出结果:</h3>
          <p><strong>选中数据对象:</strong></p>
          <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
            {JSON.stringify(selectedData2, null, 2)}
          </pre>
          
          <p><strong>平铺层级数组:</strong></p>
          <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
            {JSON.stringify(flattenedSelections2, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default CascadeSelectExample;
