# 级联选择组件 (useCascadeSelect)

一个基于 React Hooks 的级联选择组件，支持不限层级的级联选择功能。

## 功能特性

- ✅ 支持不限层级的级联选择
- ✅ 自动级联重置：某一级选择变更时，其子级重置选中第一项
- ✅ 支持初始选中项配置
- ✅ 提供完整的选中数据对象和平铺层级数组
- ✅ 内置辅助函数简化使用
- ✅ TypeScript 友好的 API 设计

## 数据结构要求

数据需要符合以下结构：

```javascript
const mockData = [
  {
    title: "显示名称",
    id: "唯一标识",
    flag: "层级标识", // 用于区分不同层级，如 'brand', 'version', 'factory', 'project'
    trPhase: "其他属性", // 可选
    child: [
      // 子级数据，结构相同
      {
        title: "子项名称",
        id: "子项ID",
        flag: "子级标识",
        trPhase: null,
        child: []
      }
    ]
  }
];
```

## API 说明

### useCascadeSelect(data, initialSelected?)

#### 参数

- `data` (Array): 全量数据数组，必须符合上述数据结构
- `initialSelected` (Object, 可选): 初始选中的数据项，格式为 `{ flag1: value1, flag2: value2, ... }`

#### 返回值

返回一个数组，包含以下四个元素：

1. `selectedData` (Object): 当前选中的数据对象，格式为 `{ flag1: value1, flag2: value2, ... }`
2. `flattenedSelections` (Array): 平铺的层级关系数组，每一项包含选中选项的完整信息
3. `setSelectedData` (Function): 修改选中数据的方法
4. `helpers` (Object): 辅助函数对象，包含：
   - `getOptionsForLevel(flag)`: 获取指定层级的选项列表
   - `handleLevelChange(flag, value)`: 处理单个层级的选择变更

## 使用示例

### 基础使用

```javascript
import React from 'react';
import useCascadeSelect from './useCascadeSelect';
import mockData from './mock';

const MyComponent = () => {
  // 默认选择（选中第一项）
  const [selectedData, flattenedSelections, setSelectedData, helpers] = useCascadeSelect(mockData);
  
  return (
    <div>
      <h3>当前选中:</h3>
      <pre>{JSON.stringify(selectedData, null, 2)}</pre>
      
      <h3>层级路径:</h3>
      <pre>{JSON.stringify(flattenedSelections, null, 2)}</pre>
    </div>
  );
};
```

### 指定初始选中项

```javascript
const MyComponent = () => {
  const initialSelected = {
    brand: 1,
    version: 1,
    factory: "180312",
    project: "O3"
  };
  
  const [selectedData, flattenedSelections, setSelectedData, helpers] = useCascadeSelect(mockData, initialSelected);
  
  // ... 组件内容
};
```

### 构建选择器界面

```javascript
const MyComponent = () => {
  const [selectedData, flattenedSelections, setSelectedData, helpers] = useCascadeSelect(mockData);
  
  const renderSelector = (flag, label) => {
    const options = helpers.getOptionsForLevel(flag);
    const shouldShow = flag === 'brand' || selectedData[getPreviousFlag(flag)];
    
    if (!shouldShow) return null;
    
    return (
      <div>
        <label>{label}: </label>
        <select 
          value={selectedData[flag] || ''} 
          onChange={(e) => helpers.handleLevelChange(flag, e.target.value)}
        >
          <option value="">请选择{label}</option>
          {options.map(item => (
            <option key={item.id} value={item.id}>{item.title}</option>
          ))}
        </select>
      </div>
    );
  };
  
  return (
    <div>
      {renderSelector('brand', '品牌')}
      {renderSelector('version', '版本')}
      {renderSelector('factory', '工厂')}
      {renderSelector('project', '项目')}
    </div>
  );
};
```

### 手动设置选中数据

```javascript
const MyComponent = () => {
  const [selectedData, flattenedSelections, setSelectedData] = useCascadeSelect(mockData);
  
  const handleCustomSelection = () => {
    // 直接设置选中数据
    setSelectedData({
      brand: 2,
      version: 1
      // 注意：子级会自动选中第一项
    });
  };
  
  const handleFunctionUpdate = () => {
    // 使用函数更新
    setSelectedData(prev => ({
      ...prev,
      factory: "129797"
    }));
  };
  
  // ... 组件内容
};
```

## 级联逻辑说明

1. **自动选择第一项**: 当没有指定选中项时，每一级都会自动选择第一个可用选项
2. **级联重置**: 当某一级的选择发生变更时，该级之后的所有子级都会被重置，并自动选中各自的第一项
3. **路径构建**: 组件会根据当前选中数据自动构建完整的层级路径
4. **容错处理**: 当指定的选中项不存在时，会自动回退到默认选择

## 注意事项

1. 数据结构中的 `flag` 字段用于区分不同层级，必须保证同一层级的所有项目具有相同的 `flag` 值
2. `id` 字段用作选中值，支持字符串和数字类型
3. 组件会自动处理类型转换，确保选中逻辑的正确性
4. 建议在数据结构设计时保持 `flag` 的命名一致性，便于维护

## 完整示例

查看 `CascadeSelectExample.jsx` 文件获取完整的使用示例。

## 测试

运行测试文件验证功能：

```bash
node test.js
```

测试包含以下场景：
- 默认选择（选中第一项）
- 指定初始选中项
- 部分指定选中项
- 无效选中项的容错处理
