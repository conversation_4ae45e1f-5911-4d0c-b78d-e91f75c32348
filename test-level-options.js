// 测试新增的 levelOptions 参数功能
import mockData from './mock.js';

// 模拟 React hooks（用于测试）
let currentState = {};
const useState = (initialValue) => {
  const setState = (newValue) => {
    if (typeof newValue === 'function') {
      currentState = newValue(currentState);
    } else {
      currentState = newValue;
    }
  };
  return [currentState, setState];
};

const useEffect = (callback, deps) => {
  callback();
};

const useCallback = (callback, deps) => {
  return callback;
};

// 简化版的 useCascadeSelect 核心逻辑（包含新的 levelOptions 功能）
const useCascadeSelectCore = (data, initialSelected = null) => {
  // 根据选中数据构建层级路径
  const buildPath = (selected) => {
    if (!data || data.length === 0) return [];
    
    const path = [];
    let currentLevel = data;
    
    // 获取所有可能的flag类型，按层级顺序
    const getAllFlags = (items, flags = []) => {
      if (!items || items.length === 0) return flags;
      
      const currentFlag = items[0]?.flag;
      if (currentFlag && !flags.includes(currentFlag)) {
        flags.push(currentFlag);
      }
      
      // 递归检查子级
      for (const item of items) {
        if (item.child && item.child.length > 0) {
          getAllFlags(item.child, flags);
          break;
        }
      }
      
      return flags;
    };
    
    const flagOrder = getAllFlags(data);
    
    // 按flag顺序构建路径
    for (const flag of flagOrder) {
      if (!currentLevel || currentLevel.length === 0) break;
      
      let selectedItem;
      
      if (selected && selected[flag]) {
        selectedItem = currentLevel.find(item => 
          item.flag === flag && 
          (item.id === selected[flag] || item.id.toString() === selected[flag].toString())
        );
      }
      
      if (!selectedItem) {
        selectedItem = currentLevel.find(item => item.flag === flag);
      }
      
      if (selectedItem) {
        path.push(selectedItem);
        currentLevel = selectedItem.child || [];
      } else {
        break;
      }
    }
    
    return path;
  };
  
  // 根据路径构建选中数据对象
  const buildSelectedData = (path) => {
    const result = {};
    path.forEach(item => {
      if (item.flag) {
        result[item.flag] = item.id;
      }
    });
    return result;
  };
  
  const path = buildPath(initialSelected);
  const selectedData = buildSelectedData(path);
  
  // 构建平铺的层级关系数组
  const flattenedSelections = path.map(item => ({
    id: item.id,
    title: item.title,
    flag: item.flag,
    trPhase: item.trPhase
  }));
  
  // 构建每一层的选项列表数据（数组平铺格式）
  const buildLevelOptions = () => {
    if (!data || data.length === 0) return [];

    const result = [];
    let currentLevel = data;

    // 获取所有可能的flag类型，按层级顺序
    const getAllFlags = (items, flags = []) => {
      if (!items || items.length === 0) return flags;

      const currentFlag = items[0]?.flag;
      if (currentFlag && !flags.includes(currentFlag)) {
        flags.push(currentFlag);
      }

      for (const item of items) {
        if (item.child && item.child.length > 0) {
          getAllFlags(item.child, flags);
          break;
        }
      }

      return flags;
    };

    const flagOrder = getAllFlags(data);
    let levelIndex = 0;

    // 按层级顺序构建每一层的选项列表
    for (const flag of flagOrder) {
      if (!currentLevel || currentLevel.length === 0) break;

      // 获取当前层级的所有选项
      const levelItems = currentLevel.filter(item => item.flag === flag);
      
      if (levelItems.length > 0) {
        // 找到当前层级的选中项
        const selectedItem = levelItems.find(item =>
          selectedData[flag] &&
          (item.id === selectedData[flag] || item.id.toString() === selectedData[flag].toString())
        );

        // 构建当前层级的选项数据
        const levelData = {
          level: levelIndex,
          flag: flag,
          options: levelItems.map(item => ({
            id: item.id,
            title: item.title,
            flag: item.flag,
            trPhase: item.trPhase,
            hasChildren: item.child && item.child.length > 0
          })),
          selectedValue: selectedData[flag] || null,
          selectedItem: selectedItem || null
        };

        result.push(levelData);

        // 如果有选中项且有子级，移动到下一层级
        if (selectedItem && selectedItem.child && selectedItem.child.length > 0) {
          currentLevel = selectedItem.child;
        } else {
          // 没有选中项或选中项没有子级，停止构建
          break;
        }
      }

      levelIndex++;
    }

    return result;
  };
  
  const levelOptions = buildLevelOptions();
  
  return [selectedData, flattenedSelections, levelOptions];
};

// 测试用例
console.log('=== 测试 levelOptions 新功能 ===\n');

// 测试1: 默认选择的层级选项
console.log('测试1: 默认选择的层级选项');
const [selectedData1, flattenedSelections1, levelOptions1] = useCascadeSelectCore(mockData);
console.log('选中数据:', JSON.stringify(selectedData1, null, 2));
console.log('层级选项数据:');
levelOptions1.forEach((level, index) => {
  console.log(`  层级 ${index} (${level.flag}):`);
  console.log(`    选中值: ${level.selectedValue}`);
  console.log(`    选项数量: ${level.options.length}`);
  console.log(`    选项列表: ${level.options.map(opt => opt.title).join(', ')}`);
});
console.log('\n');

// 测试2: 指定选中项的层级选项
console.log('测试2: 指定选中项的层级选项');
const initialSelected = {
  brand: 1,
  version: 1,
  factory: "180312",
  project: "O3"
};
const [selectedData2, flattenedSelections2, levelOptions2] = useCascadeSelectCore(mockData, initialSelected);
console.log('初始选中:', JSON.stringify(initialSelected, null, 2));
console.log('层级选项数据:');
levelOptions2.forEach((level, index) => {
  console.log(`  层级 ${index} (${level.flag}):`);
  console.log(`    选中值: ${level.selectedValue}`);
  console.log(`    选中项: ${level.selectedItem?.title || 'null'}`);
  console.log(`    选项数量: ${level.options.length}`);
  console.log(`    选项列表: ${level.options.map(opt => `${opt.title}${opt.trPhase ? `(${opt.trPhase})` : ''}`).join(', ')}`);
});
console.log('\n');

// 测试3: 部分选中项的层级选项
console.log('测试3: 部分选中项的层级选项');
const partialSelected = {
  brand: 2,
  version: 1
};
const [selectedData3, flattenedSelections3, levelOptions3] = useCascadeSelectCore(mockData, partialSelected);
console.log('部分选中:', JSON.stringify(partialSelected, null, 2));
console.log('层级选项数据:');
levelOptions3.forEach((level, index) => {
  console.log(`  层级 ${index} (${level.flag}):`);
  console.log(`    选中值: ${level.selectedValue}`);
  console.log(`    选项数量: ${level.options.length}`);
  console.log(`    选项列表: ${level.options.map(opt => opt.title).join(', ')}`);
});

console.log('\n=== 测试完成 ===');
