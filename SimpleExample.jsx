import React from 'react';
import useCascadeSelect from './useCascadeSelect';
import mockData from './mock';

/**
 * 简化版级联选择示例
 * 展示最基本的使用方法
 */
const SimpleExample = () => {
  // 使用级联选择Hook
  const [selectedData, flattenedSelections, setSelectedData, helpers] = useCascadeSelect(mockData);
  
  // 层级配置
  const levels = [
    { flag: 'brand', label: '品牌' },
    { flag: 'version', label: '版本' },
    { flag: 'factory', label: '工厂' },
    { flag: 'project', label: '项目' }
  ];
  
  // 检查某个层级是否应该显示
  const shouldShowLevel = (currentFlag) => {
    const currentIndex = levels.findIndex(level => level.flag === currentFlag);
    if (currentIndex === 0) return true; // 第一级总是显示
    
    const previousFlag = levels[currentIndex - 1]?.flag;
    return previousFlag && selectedData[previousFlag];
  };
  
  return (
    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <h2>级联选择示例</h2>
      
      {/* 渲染所有选择器 */}
      <div style={{ marginBottom: '30px' }}>
        {levels.map(({ flag, label }) => {
          if (!shouldShowLevel(flag)) return null;
          
          const options = helpers.getOptionsForLevel(flag);
          
          return (
            <div key={flag} style={{ marginBottom: '15px' }}>
              <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                {label}:
              </label>
              <select
                value={selectedData[flag] || ''}
                onChange={(e) => helpers.handleLevelChange(flag, e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  fontSize: '14px'
                }}
              >
                <option value="">请选择{label}</option>
                {options.map(item => (
                  <option key={item.id} value={item.id}>
                    {item.title}
                  </option>
                ))}
              </select>
            </div>
          );
        })}
      </div>
      
      {/* 显示选中结果 */}
      <div style={{ backgroundColor: '#f5f5f5', padding: '15px', borderRadius: '8px' }}>
        <h3 style={{ marginTop: 0 }}>选中结果:</h3>
        
        {/* 选中路径 */}
        <div style={{ marginBottom: '15px' }}>
          <strong>选中路径:</strong>
          <div style={{ marginTop: '5px' }}>
            {flattenedSelections.map((item, index) => (
              <span key={item.flag}>
                {item.title}
                {index < flattenedSelections.length - 1 && ' → '}
              </span>
            ))}
          </div>
        </div>
        
        {/* 选中数据对象 */}
        <div style={{ marginBottom: '15px' }}>
          <strong>选中数据:</strong>
          <pre style={{ 
            backgroundColor: 'white', 
            padding: '10px', 
            borderRadius: '4px',
            fontSize: '12px',
            overflow: 'auto'
          }}>
            {JSON.stringify(selectedData, null, 2)}
          </pre>
        </div>
        
        {/* 平铺层级数组 */}
        <div>
          <strong>层级详情:</strong>
          <pre style={{ 
            backgroundColor: 'white', 
            padding: '10px', 
            borderRadius: '4px',
            fontSize: '12px',
            overflow: 'auto'
          }}>
            {JSON.stringify(flattenedSelections, null, 2)}
          </pre>
        </div>
      </div>
      
      {/* 操作按钮 */}
      <div style={{ marginTop: '20px' }}>
        <button
          onClick={() => setSelectedData({})}
          style={{
            padding: '8px 16px',
            marginRight: '10px',
            backgroundColor: '#ff4d4f',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          重置选择
        </button>
        
        <button
          onClick={() => setSelectedData({
            brand: 2,
            version: 1,
            factory: "129797",
            project: "L81A"
          })}
          style={{
            padding: '8px 16px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          设置为平板示例
        </button>
      </div>
    </div>
  );
};

export default SimpleExample;
