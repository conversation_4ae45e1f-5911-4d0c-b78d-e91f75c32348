import { findMatchingItems, getHierarchyPath } from './utils.js';

// 使用示例

// 1. 查找具体项目的匹配数据
console.log('=== 示例1: 查找具体项目 ===');
const projectMatch = findMatchingItems({
  brand: 1,
  version: 1,
  factory: '180312',
  project: 'O12U'
});

console.log('匹配到的项目:', projectMatch[0]);
console.log('完整路径:', projectMatch[0].matchPath);

// 2. 查找工厂下的所有项目
console.log('\n=== 示例2: 查找工厂下的所有项目 ===');
const factoryProjects = findMatchingItems({
  brand: 1,
  version: 1,
  factory: '180312'
});

console.log(`长沙蓝思工厂共有 ${factoryProjects.length} 个匹配项:`);
factoryProjects.forEach(item => {
  if (item.flag === 'project') {
    console.log(`- 项目: ${item.title} (${item.id}), 阶段: ${item.trPhase || '未设置'}`);
  }
});

// 3. 获取完整的层级路径信息
console.log('\n=== 示例3: 获取完整层级路径 ===');
const hierarchy = getHierarchyPath({
  brand: 1,
  version: 1,
  factory: '180312',
  project: 'O12U'
});

console.log('完整层级信息:');
Object.entries(hierarchy).forEach(([flag, info]) => {
  console.log(`${flag}: ${info.title} (ID: ${info.id})`);
});

// 4. 查找印度版本的数据
console.log('\n=== 示例4: 查找印度版本数据 ===');
const indiaData = findMatchingItems({
  brand: 1,
  version: 3  // 印度版本
});

console.log(`印度版本共有 ${indiaData.length} 个匹配项`);
const indiaFactories = indiaData.filter(item => item.flag === 'factory');
console.log('印度工厂列表:');
indiaFactories.forEach(factory => {
  console.log(`- ${factory.title} (${factory.id})`);
});

// 5. 查找平板产品
console.log('\n=== 示例5: 查找平板产品 ===');
const tabletData = findMatchingItems({
  brand: 2  // 平板
});

console.log(`平板产品共有 ${tabletData.length} 个匹配项`);
const tabletProjects = tabletData.filter(item => item.flag === 'project');
console.log('平板项目列表:');
tabletProjects.forEach(project => {
  console.log(`- ${project.title} (${project.id}), 阶段: ${project.trPhase || '未设置'}`);
});
