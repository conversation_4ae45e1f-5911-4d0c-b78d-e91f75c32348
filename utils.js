/**
 * 根据给定的flag属性名对象值，匹配并返回对应的数据项
 * @param {Object} flagValues - 包含flag属性名和对应值的对象，如 {brand: 1, version: 1, factory: '180312', project: 'O12U'}
 * @returns {Array} 匹配的数据项数组
 */
function findMatchingItems(flagValues) {
  const results = [];

  // 递归搜索函数
  function searchNode(node, currentPath = {}) {
    // 记录当前节点的flag和id到路径中
    if (node.flag && node.id !== undefined) {
      currentPath = { ...currentPath, [node.flag]: node.id };
    }

    // 检查当前节点是否匹配所有给定的flag条件
    const isMatch = Object.keys(flagValues).every(flag => {
      return currentPath[flag] !== undefined &&
             String(currentPath[flag]) === String(flagValues[flag]);
    });

    // 如果匹配，将当前节点添加到结果中
    if (isMatch) {
      results.push({
        ...node,
        matchPath: currentPath
      });
    }

    // 递归搜索子节点
    if (node.child && Array.isArray(node.child)) {
      node.child.forEach(childNode => {
        searchNode(childNode, currentPath);
      });
    }
  }

  // 从根节点开始搜索
  mockData.forEach(rootNode => {
    searchNode(rootNode);
  });

  return results;
}

/**
 * 获取指定路径的完整层级信息
 * @param {Object} flagValues - 包含flag属性名和对应值的对象
 * @returns {Object|null} 完整的层级路径信息，如果未找到返回null
 */
function getHierarchyPath(flagValues) {
  const results = findMatchingItems(flagValues);

  if (results.length === 0) {
    return null;
  }

  // 返回第一个匹配项的完整路径信息
  const match = results[0];
  const pathInfo = {};

  // 构建完整的层级路径
  function buildPath(node, targetPath) {
    if (node.flag && node.id !== undefined) {
      if (targetPath[node.flag] !== undefined &&
          String(node.id) === String(targetPath[node.flag])) {
        pathInfo[node.flag] = {
          id: node.id,
          title: node.title,
          trPhase: node.trPhase
        };

        // 继续查找子节点
        if (node.child && Array.isArray(node.child)) {
          node.child.forEach(childNode => {
            buildPath(childNode, targetPath);
          });
        }
      }
    }

    // 递归搜索子节点
    if (node.child && Array.isArray(node.child)) {
      node.child.forEach(childNode => {
        buildPath(childNode, targetPath);
      });
    }
  }

  mockData.forEach(rootNode => {
    buildPath(rootNode, match.matchPath);
  });

  return pathInfo;
}

export { findMatchingItems, getHierarchyPath };