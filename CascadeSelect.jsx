import React from 'react';
import mockData from './mock.js';
import useCascadeSelect from './useCascadeSelect.js';

const CascadeSelect = () => {
  const {
    selectedBrand,
    selectedVersion,
    selectedFactory,
    selectedProject,
    setSelectedBrand,
    setSelectedVersion,
    setSelectedFactory,
    setSelectedProject,
    brands,
    versions,
    factories,
    projects
  } = useCascadeSelect(mockData);

  const selectStyle = {
    padding: '8px 12px',
    margin: '0 10px 10px 0',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontSize: '14px',
    minWidth: '150px'
  };

  const containerStyle = {
    padding: '20px',
    fontFamily: 'Arial, sans-serif'
  };

  return (
    <div style={containerStyle}>
      <h3>产品级联选择</h3>
      
      <div style={{ marginBottom: '20px' }}>
        <select
          style={selectStyle}
          value={selectedBrand}
          onChange={(e) => setSelectedBrand(e.target.value)}
        >
          <option value="">请选择品牌</option>
          {brands.map(brand => (
            <option key={brand.id} value={brand.id}>
              {brand.title}
            </option>
          ))}
        </select>

        <select
          style={selectStyle}
          value={selectedVersion}
          onChange={(e) => setSelectedVersion(e.target.value)}
          disabled={!selectedBrand}
        >
          <option value="">请选择版本</option>
          {versions.map(version => (
            <option key={version.id} value={version.id}>
              {version.title}
            </option>
          ))}
        </select>

        <select
          style={selectStyle}
          value={selectedFactory}
          onChange={(e) => setSelectedFactory(e.target.value)}
          disabled={!selectedVersion}
        >
          <option value="">请选择工厂</option>
          {factories.map(factory => (
            <option key={factory.id} value={factory.id}>
              {factory.title}
            </option>
          ))}
        </select>

        <select
          style={selectStyle}
          value={selectedProject}
          onChange={(e) => setSelectedProject(e.target.value)}
          disabled={!selectedFactory}
        >
          <option value="">请选择项目</option>
          {projects.map(project => (
            <option key={project.id} value={project.id}>
              {project.title} {project.trPhase && `(${project.trPhase})`}
            </option>
          ))}
        </select>
      </div>

      {selectedProject && (
        <div style={{ 
          padding: '10px', 
          backgroundColor: '#f5f5f5', 
          borderRadius: '4px',
          marginTop: '20px'
        }}>
          <h4>选择结果：</h4>
          <p>品牌: {brands.find(b => b.id.toString() === selectedBrand)?.title}</p>
          <p>版本: {versions.find(v => v.id.toString() === selectedVersion)?.title}</p>
          <p>工厂: {factories.find(f => f.id === selectedFactory)?.title}</p>
          <p>项目: {projects.find(p => p.id === selectedProject)?.title}</p>
        </div>
      )}
    </div>
  );
};

export default CascadeSelect;
