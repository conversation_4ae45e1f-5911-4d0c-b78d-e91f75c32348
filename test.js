// 简单的测试文件，用于验证 useCascadeSelect 的功能
import mockData from './mock.js';

// 模拟 React hooks（用于测试）
let currentState = {};
const useState = (initialValue) => {
  const setState = (newValue) => {
    if (typeof newValue === 'function') {
      currentState = newValue(currentState);
    } else {
      currentState = newValue;
    }
  };
  return [currentState, setState];
};

const useEffect = (callback, deps) => {
  callback();
};

const useCallback = (callback, deps) => {
  return callback;
};

// 导入 useCascadeSelect 的核心逻辑
const useCascadeSelectCore = (data, initialSelected = null) => {
  // 根据选中数据构建层级路径
  const buildPath = (selected) => {
    if (!data || data.length === 0) return [];
    
    const path = [];
    let currentLevel = data;
    
    // 获取所有可能的flag类型，按层级顺序
    const getAllFlags = (items, flags = []) => {
      if (!items || items.length === 0) return flags;
      
      const currentFlag = items[0]?.flag;
      if (currentFlag && !flags.includes(currentFlag)) {
        flags.push(currentFlag);
      }
      
      // 递归检查子级
      for (const item of items) {
        if (item.child && item.child.length > 0) {
          getAllFlags(item.child, flags);
          break; // 只需要检查第一个有子级的项目即可
        }
      }
      
      return flags;
    };
    
    const flagOrder = getAllFlags(data);
    
    // 按flag顺序构建路径
    for (const flag of flagOrder) {
      if (!currentLevel || currentLevel.length === 0) break;
      
      let selectedItem;
      
      if (selected && selected[flag]) {
        // 如果有指定选中项，查找对应项
        selectedItem = currentLevel.find(item => 
          item.flag === flag && 
          (item.id === selected[flag] || item.id.toString() === selected[flag].toString())
        );
      }
      
      // 如果没有找到指定项或没有指定，选择第一项
      if (!selectedItem) {
        selectedItem = currentLevel.find(item => item.flag === flag);
      }
      
      if (selectedItem) {
        path.push(selectedItem);
        currentLevel = selectedItem.child || [];
      } else {
        break;
      }
    }
    
    return path;
  };
  
  // 根据路径构建选中数据对象
  const buildSelectedData = (path) => {
    const result = {};
    path.forEach(item => {
      if (item.flag) {
        result[item.flag] = item.id;
      }
    });
    return result;
  };
  
  const path = buildPath(initialSelected);
  const selectedData = buildSelectedData(path);
  
  // 构建平铺的层级关系数组
  const flattenedSelections = path.map(item => ({
    id: item.id,
    title: item.title,
    flag: item.flag,
    trPhase: item.trPhase
  }));
  
  return [selectedData, flattenedSelections];
};

// 测试用例
console.log('=== 测试 useCascadeSelect ===\n');

// 测试1: 默认选择（选中第一项）
console.log('测试1: 默认选择');
const [selectedData1, flattenedSelections1] = useCascadeSelectCore(mockData);
console.log('选中数据对象:', JSON.stringify(selectedData1, null, 2));
console.log('平铺层级数组:', JSON.stringify(flattenedSelections1, null, 2));
console.log('\n');

// 测试2: 指定初始选中项
console.log('测试2: 指定初始选中项');
const initialSelected = {
  brand: 1,      // 手机
  version: 1,    // 国内
  factory: "180312", // 长沙蓝思
  project: "O3"  // O3项目
};
const [selectedData2, flattenedSelections2] = useCascadeSelectCore(mockData, initialSelected);
console.log('初始选中:', JSON.stringify(initialSelected, null, 2));
console.log('选中数据对象:', JSON.stringify(selectedData2, null, 2));
console.log('平铺层级数组:', JSON.stringify(flattenedSelections2, null, 2));
console.log('\n');

// 测试3: 部分指定选中项
console.log('测试3: 部分指定选中项（只指定品牌和版本）');
const partialSelected = {
  brand: 2,      // 平板
  version: 1     // 国内
};
const [selectedData3, flattenedSelections3] = useCascadeSelectCore(mockData, partialSelected);
console.log('部分选中:', JSON.stringify(partialSelected, null, 2));
console.log('选中数据对象:', JSON.stringify(selectedData3, null, 2));
console.log('平铺层级数组:', JSON.stringify(flattenedSelections3, null, 2));
console.log('\n');

// 测试4: 无效的选中项
console.log('测试4: 无效的选中项');
const invalidSelected = {
  brand: 999,    // 不存在的品牌
  version: 1
};
const [selectedData4, flattenedSelections4] = useCascadeSelectCore(mockData, invalidSelected);
console.log('无效选中:', JSON.stringify(invalidSelected, null, 2));
console.log('选中数据对象:', JSON.stringify(selectedData4, null, 2));
console.log('平铺层级数组:', JSON.stringify(flattenedSelections4, null, 2));

console.log('\n=== 测试完成 ===');
