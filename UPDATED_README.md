# useCascadeSelect Hook - 更新版本

## 🆕 新增功能

在原有功能基础上，新增了第五个返回参数 `levelOptions`，提供每一层的选项列表数据，以数组平铺格式返回。

## API 更新

### useCascadeSelect(data, initialSelected?)

#### 返回值（更新）

现在返回 **5个参数**：

```javascript
const [selectedData, flattenedSelections, setSelectedData, helpers, levelOptions] = useCascadeSelect(mockData);
```

1. `selectedData` (Object): 当前选中的数据对象
2. `flattenedSelections` (Array): 平铺的层级关系数组  
3. `setSelectedData` (Function): 修改选中数据的方法
4. `helpers` (Object): 辅助函数对象
5. **`levelOptions` (Array): 每一层的选项列表数据** ⭐ **新增**

## levelOptions 数据结构

`levelOptions` 是一个数组，每个元素代表一个层级的完整信息：

```javascript
[
  {
    level: 0,                    // 层级索引（从0开始）
    flag: "brand",              // 层级标识
    options: [                  // 当前层级的所有选项
      {
        id: 1,
        title: "手机",
        flag: "brand",
        trPhase: null,
        hasChildren: true       // 是否有子级
      },
      {
        id: 2,
        title: "平板", 
        flag: "brand",
        trPhase: null,
        hasChildren: true
      }
    ],
    selectedValue: 1,           // 当前层级的选中值
    selectedItem: {             // 当前层级的选中项完整信息
      id: 1,
      title: "手机",
      flag: "brand", 
      trPhase: null,
      hasChildren: true
    }
  },
  {
    level: 1,
    flag: "version",
    options: [...],
    selectedValue: 1,
    selectedItem: {...}
  }
  // ... 更多层级
]
```

## 使用示例

### 基础使用

```javascript
import React from 'react';
import useCascadeSelect from './useCascadeSelect';
import mockData from './mock';

const MyComponent = () => {
  // 注意：现在返回5个参数
  const [selectedData, flattenedSelections, setSelectedData, helpers, levelOptions] = useCascadeSelect(mockData);
  
  return (
    <div>
      <h3>层级选项数据：</h3>
      {levelOptions.map((level) => (
        <div key={level.flag}>
          <h4>层级 {level.level}: {level.flag}</h4>
          <p>选中值: {level.selectedValue}</p>
          <p>选项数量: {level.options.length}</p>
          <p>选项列表: {level.options.map(opt => opt.title).join(', ')}</p>
        </div>
      ))}
    </div>
  );
};
```

### 构建动态选择器

```javascript
const DynamicSelector = () => {
  const [selectedData, flattenedSelections, setSelectedData, helpers, levelOptions] = useCascadeSelect(mockData);
  
  return (
    <div>
      {levelOptions.map((level) => (
        <div key={level.flag}>
          <label>{level.flag}:</label>
          <select
            value={level.selectedValue || ''}
            onChange={(e) => helpers.handleLevelChange(level.flag, e.target.value)}
          >
            <option value="">请选择{level.flag}</option>
            {level.options.map(option => (
              <option key={option.id} value={option.id}>
                {option.title} {option.trPhase && `(${option.trPhase})`}
              </option>
            ))}
          </select>
        </div>
      ))}
    </div>
  );
};
```

### 显示选项统计信息

```javascript
const OptionsStats = () => {
  const [selectedData, flattenedSelections, setSelectedData, helpers, levelOptions] = useCascadeSelect(mockData);
  
  return (
    <div>
      <h3>选项统计：</h3>
      {levelOptions.map((level) => (
        <div key={level.flag}>
          <p>
            <strong>{level.flag}</strong>: 
            {level.options.length} 个选项，
            已选中 "{level.selectedItem?.title || '未选中'}"
            {level.selectedItem?.hasChildren && ' (有子级)'}
          </p>
        </div>
      ))}
    </div>
  );
};
```

## 应用场景

### 1. 动态表单生成
根据 `levelOptions` 动态生成表单字段，无需硬编码层级结构。

### 2. 选项统计分析
分析每个层级的选项数量、选中状态等信息。

### 3. 自定义UI组件
基于 `levelOptions` 构建自定义的级联选择UI，如标签页、卡片等。

### 4. 数据验证
检查每个层级的选项完整性和选中状态。

## 兼容性说明

- ✅ **向后兼容**：现有代码可以继续使用前4个返回值
- ✅ **渐进增强**：可以选择性地使用新的 `levelOptions` 参数
- ✅ **类型安全**：新参数遵循相同的类型约定

## 测试验证

运行测试文件验证新功能：

```bash
node test-level-options.js
```

测试覆盖：
- ✅ 默认选择的层级选项
- ✅ 指定初始选中项的层级选项  
- ✅ 部分选中项的层级选项
- ✅ 层级选项数据结构完整性

## 完整示例

查看以下文件获取完整示例：
- `levelOptions-example.jsx` - 新功能演示
- `index.html` - 浏览器可运行示例
- `test-level-options.js` - 功能测试

## 总结

新增的 `levelOptions` 参数提供了更强大的数据访问能力，使得开发者可以：

1. **更灵活地构建UI**：基于实际数据结构动态生成界面
2. **更好的用户体验**：显示选项统计、状态指示等信息
3. **更强的扩展性**：支持复杂的业务逻辑和自定义组件
4. **更易于调试**：完整的层级数据便于问题排查

这个更新保持了API的简洁性，同时大大增强了组件的功能性和灵活性。
