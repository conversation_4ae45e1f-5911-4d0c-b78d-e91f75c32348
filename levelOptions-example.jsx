import React from 'react';
import useCascadeSelect from './useCascadeSelect';
import mockData from './mock';

/**
 * 展示 levelOptions 新功能的示例
 */
const LevelOptionsExample = () => {
  // 使用级联选择Hook，现在返回5个参数
  const [selectedData, flattenedSelections, setSelectedData, helpers, levelOptions] = useCascadeSelect(mockData);
  
  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>levelOptions 功能演示</h2>
      
      {/* 显示层级选项数据 */}
      <div style={{ marginBottom: '30px' }}>
        <h3>每一层的选项列表数据：</h3>
        {levelOptions.map((level, index) => (
          <div key={level.flag} style={{ 
            marginBottom: '20px', 
            padding: '15px', 
            border: '1px solid #ddd', 
            borderRadius: '8px',
            backgroundColor: '#f9f9f9'
          }}>
            <h4 style={{ margin: '0 0 10px 0', color: '#333' }}>
              层级 {level.level}: {level.flag}
            </h4>
            
            <div style={{ marginBottom: '10px' }}>
              <strong>选中值：</strong> 
              <span style={{ color: '#007bff' }}>
                {level.selectedValue || '未选中'}
              </span>
            </div>
            
            <div style={{ marginBottom: '10px' }}>
              <strong>选中项：</strong> 
              <span style={{ color: '#28a745' }}>
                {level.selectedItem ? level.selectedItem.title : '未选中'}
              </span>
            </div>
            
            <div style={{ marginBottom: '10px' }}>
              <strong>选项数量：</strong> {level.options.length}
            </div>
            
            <div>
              <strong>所有选项：</strong>
              <div style={{ 
                marginTop: '5px', 
                display: 'flex', 
                flexWrap: 'wrap', 
                gap: '5px' 
              }}>
                {level.options.map(option => (
                  <span 
                    key={option.id} 
                    style={{
                      padding: '4px 8px',
                      backgroundColor: option.id === level.selectedValue ? '#007bff' : '#e9ecef',
                      color: option.id === level.selectedValue ? 'white' : '#495057',
                      borderRadius: '4px',
                      fontSize: '12px',
                      border: option.hasChildren ? '2px solid #28a745' : '1px solid transparent'
                    }}
                    title={option.hasChildren ? '有子级' : '无子级'}
                  >
                    {option.title}
                    {option.trPhase && ` (${option.trPhase})`}
                  </span>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {/* 传统的选择器界面 */}
      <div style={{ marginBottom: '30px' }}>
        <h3>选择器界面：</h3>
        {levelOptions.map((level) => (
          <div key={level.flag} style={{ marginBottom: '15px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
              {level.flag}:
            </label>
            <select
              value={level.selectedValue || ''}
              onChange={(e) => helpers.handleLevelChange(level.flag, e.target.value)}
              style={{
                width: '100%',
                padding: '8px',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            >
              <option value="">请选择{level.flag}</option>
              {level.options.map(option => (
                <option key={option.id} value={option.id}>
                  {option.title} {option.trPhase && `(${option.trPhase})`}
                </option>
              ))}
            </select>
          </div>
        ))}
      </div>
      
      {/* 显示其他返回值 */}
      <div style={{ 
        backgroundColor: '#f8f9fa', 
        padding: '15px', 
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h3>其他返回值：</h3>
        
        <div style={{ marginBottom: '15px' }}>
          <strong>selectedData:</strong>
          <pre style={{ 
            backgroundColor: 'white', 
            padding: '10px', 
            borderRadius: '4px',
            fontSize: '12px',
            overflow: 'auto'
          }}>
            {JSON.stringify(selectedData, null, 2)}
          </pre>
        </div>
        
        <div style={{ marginBottom: '15px' }}>
          <strong>flattenedSelections:</strong>
          <pre style={{ 
            backgroundColor: 'white', 
            padding: '10px', 
            borderRadius: '4px',
            fontSize: '12px',
            overflow: 'auto'
          }}>
            {JSON.stringify(flattenedSelections, null, 2)}
          </pre>
        </div>
      </div>
      
      {/* 操作按钮 */}
      <div>
        <button
          onClick={() => setSelectedData({})}
          style={{
            padding: '8px 16px',
            marginRight: '10px',
            backgroundColor: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          重置选择
        </button>
        
        <button
          onClick={() => setSelectedData({
            brand: 2,
            version: 1,
            factory: "160434",
            project: "M81"
          })}
          style={{
            padding: '8px 16px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          设置为平板示例
        </button>
      </div>
      
      {/* 使用说明 */}
      <div style={{ 
        marginTop: '30px', 
        padding: '15px', 
        backgroundColor: '#e7f3ff', 
        borderRadius: '8px',
        border: '1px solid #b3d9ff'
      }}>
        <h3 style={{ color: '#0056b3' }}>levelOptions 数据结构说明：</h3>
        <ul style={{ paddingLeft: '20px', lineHeight: '1.6' }}>
          <li><strong>level</strong>: 层级索引（从0开始）</li>
          <li><strong>flag</strong>: 层级标识（如 brand, version, factory, project）</li>
          <li><strong>options</strong>: 当前层级的所有可选项数组</li>
          <li><strong>selectedValue</strong>: 当前层级的选中值</li>
          <li><strong>selectedItem</strong>: 当前层级的选中项完整信息</li>
        </ul>
        <p style={{ marginTop: '10px', fontSize: '14px', color: '#666' }}>
          绿色边框表示该选项有子级，蓝色背景表示当前选中项。
        </p>
      </div>
    </div>
  );
};

export default LevelOptionsExample;
