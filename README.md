# Mock数据匹配工具

这个工具提供了两个主要方法来查询和匹配mock.js中的树形数据结构。

## 数据结构说明

数据是一个树形结构，包含以下层级：
- `brand`: 品牌层级（手机=1, 平板=2）
- `version`: 版本层级（国内=1, 国际=2, 印度=3）
- `factory`: 工厂层级（各工厂的ID）
- `project`: 项目层级（各项目的ID）

每个节点包含以下属性：
- `id`: 节点标识
- `title`: 节点标题
- `flag`: 节点类型标识
- `trPhase`: 项目阶段（可能为null）
- `child`: 子节点数组

## 方法说明

### 1. findMatchingItems(flagValues)

根据给定的flag属性名对象值，匹配并返回对应的数据项。

**参数：**
- `flagValues`: 包含flag属性名和对应值的对象

**返回值：**
- 匹配的数据项数组，每个项目包含原始数据和`matchPath`属性

**示例：**
```javascript
import { findMatchingItems } from './mock.js';

// 查找具体项目
const result = findMatchingItems({
  brand: 1,
  version: 1,
  factory: '180312',
  project: 'O12U'
});

// 查找工厂下的所有项目
const factoryItems = findMatchingItems({
  brand: 1,
  version: 1,
  factory: '180312'
});

// 查找版本下的所有数据
const versionItems = findMatchingItems({
  brand: 1,
  version: 1
});
```

### 2. getHierarchyPath(flagValues)

获取指定路径的完整层级信息。

**参数：**
- `flagValues`: 包含flag属性名和对应值的对象

**返回值：**
- 完整的层级路径信息对象，如果未找到返回null

**示例：**
```javascript
import { getHierarchyPath } from './mock.js';

const hierarchy = getHierarchyPath({
  brand: 1,
  version: 1,
  factory: '180312',
  project: 'O12U'
});

console.log(hierarchy);
// 输出:
// {
//   brand: { id: 1, title: '手机', trPhase: null },
//   version: { id: 1, title: '国内', trPhase: null },
//   factory: { id: '180312', title: '长沙蓝思', trPhase: null },
//   project: { id: 'O12U', title: 'O12U', trPhase: null }
// }
```

## 使用场景

1. **精确匹配**: 根据完整路径查找特定项目
2. **层级查询**: 查找某个工厂下的所有项目
3. **批量查询**: 查找某个版本下的所有数据
4. **路径解析**: 获取完整的层级路径信息

## 运行测试

```bash
# 运行完整测试
node test-mock.js

# 运行使用示例
node example.js
```

## 注意事项

- ID比较时会转换为字符串进行匹配，支持数字和字符串类型的ID
- 方法会递归搜索整个树结构
- 匹配是精确匹配，必须完全符合给定的条件
- 如果给定的条件不完整，会返回所有符合部分条件的节点
