import mockData from './mock.js';
import { findMatchingItems, getHierarchyPath } from './utils.js'

// 测试用例
console.log('=== Mock数据匹配测试 ===\n');

// 测试1: 匹配具体的项目
console.log('测试1: 匹配 {brand: 1, version: 1, factory: "180312", project: "O12U"}');
const test1 = findMatchingItems({
  brand: 1,
  version: 1, 
  factory: '180312',
  project: 'O12U'
});
console.log('匹配结果数量:', test1.length);
test1.forEach((item, index) => {
  console.log(`结果${index + 1}:`, {
    title: item.title,
    id: item.id,
    flag: item.flag,
    trPhase: item.trPhase,
    matchPath: item.matchPath
  });
});
console.log('\n');

// 测试2: 匹配工厂级别
console.log('测试2: 匹配 {brand: 1, version: 1, factory: "180312"}');
const test2 = findMatchingItems({
  brand: 1,
  version: 1,
  factory: '180312'
});
console.log('匹配结果数量:', test2.length);
test2.forEach((item, index) => {
  console.log(`结果${index + 1}:`, {
    title: item.title,
    id: item.id,
    flag: item.flag,
    trPhase: item.trPhase,
    matchPath: item.matchPath
  });
});
console.log('\n');

// 测试3: 匹配版本级别
console.log('测试3: 匹配 {brand: 1, version: 1}');
const test3 = findMatchingItems({
  brand: 1,
  version: 1
});
console.log('匹配结果数量:', test3.length);
console.log('前5个结果:');
test3.slice(0, 5).forEach((item, index) => {
  console.log(`结果${index + 1}:`, {
    title: item.title,
    id: item.id,
    flag: item.flag,
    trPhase: item.trPhase
  });
});
console.log('\n');

// 测试4: 匹配品牌级别
console.log('测试4: 匹配 {brand: 2}');
const test4 = findMatchingItems({
  brand: 2
});
console.log('匹配结果数量:', test4.length);
console.log('前3个结果:');
test4.slice(0, 3).forEach((item, index) => {
  console.log(`结果${index + 1}:`, {
    title: item.title,
    id: item.id,
    flag: item.flag,
    trPhase: item.trPhase
  });
});
console.log('\n');

// 测试5: 获取层级路径信息
console.log('测试5: 获取层级路径 {brand: 1, version: 1, factory: "180312", project: "O12U"}');
const hierarchyPath = getHierarchyPath({
  brand: 1,
  version: 1,
  factory: '180312', 
  project: 'O12U'
});
console.log('层级路径信息:', hierarchyPath);
console.log('\n');

// 测试6: 匹配不存在的数据
console.log('测试6: 匹配不存在的数据 {brand: 999}');
const test6 = findMatchingItems({
  brand: 999
});
console.log('匹配结果数量:', test6.length);
console.log('\n');

// 测试7: 匹配印度版本的项目
console.log('测试7: 匹配印度版本 {brand: 1, version: 3, factory: "123503", project: "M20"}');
const test7 = findMatchingItems({
  brand: 1,
  version: 3,
  factory: '123503',
  project: 'M20'
});
console.log('匹配结果数量:', test7.length);
test7.forEach((item, index) => {
  console.log(`结果${index + 1}:`, {
    title: item.title,
    id: item.id,
    flag: item.flag,
    trPhase: item.trPhase,
    matchPath: item.matchPath
  });
});

console.log('\n=== 测试完成 ===');
